<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Project_report extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('projects_m');
        $this->load->model('users_m');
        $this->load->model('task_assign_m');
        $this->load->model('tasks_m');
        ini_set('pcre.backtrack_limit', 100000000);
        ini_set('memory_limit', '2048M');
    }

    public function project_employee() {
        if (!has_permission('project_report/project_employee')){
            redirect('app/dashboard/index');
        }

        $project_id = $this->input->get('project_id') ?? 0;
        $start_date = $this->input->get('start_date') ?? '';
        $end_date = $this->input->get('end_date') ?? '';

        $this->data['report_data'] = [];
        $this->data['total_hours'] = '0 hrs 0 min';
        $this->data['column_totals'] = [
            'total_date_range' => '0 hrs 0 min',
            'last_7_days' => '0 hrs 0 min',
            'last_15_days' => '0 hrs 0 min',
            'last_30_days' => '0 hrs 0 min',
            'last_60_days' => '0 hrs 0 min',
            'last_90_days' => '0 hrs 0 min',
            'last_180_days' => '0 hrs 0 min',
            'total_worked' => '0 hrs 0 min'
        ];

        $this->data['emp_cost_totals'] = [
            'total_date_range_emp_cost' => '0.00',
            'last_7_days_emp_cost' => '0.00',
            'last_15_days_emp_cost' => '0.00',
            'last_30_days_emp_cost' => '0.00',
            'last_60_days_emp_cost' => '0.00',
            'last_90_days_emp_cost' => '0.00',
            'last_180_days_emp_cost' => '0.00',
            'total_worked_emp_cost' => '0.00'
        ];

        $this->data['proj_cost_totals'] = [
            'total_date_range_proj_cost' => '0.00',
            'last_7_days_proj_cost' => '0.00',
            'last_15_days_proj_cost' => '0.00',
            'last_30_days_proj_cost' => '0.00',
            'last_60_days_proj_cost' => '0.00',
            'last_90_days_proj_cost' => '0.00',
            'last_180_days_proj_cost' => '0.00',
            'total_worked_proj_cost' => '0.00'
        ];

        // Generate report if project is selected
        if (!empty($project_id)) {
            // Set default dates if not provided
            if (empty($start_date) || empty($end_date)) {
                // Default to this month if no dates provided
                $end_date = date('Y-m-d');
                $start_date = date('Y-m-01'); // First day of current month
            }

            // Validate date format if dates are provided
            $date_valid = true;
            $date_error = '';

            if (!DateTime::createFromFormat('Y-m-d', $start_date)) {
                $date_valid = false;
                $date_error = 'Invalid start date format. Please use YYYY-MM-DD format.';
            } elseif (!DateTime::createFromFormat('Y-m-d', $end_date)) {
                $date_valid = false;
                $date_error = 'Invalid end date format. Please use YYYY-MM-DD format.';
            } elseif (strtotime($start_date) > strtotime($end_date)) {
                $date_valid = false;
                $date_error = 'Start date cannot be after end date.';
            }

            if ($date_valid) {
                $this->data['report_data'] = $this->get_project_employee_report($project_id, $start_date, $end_date);
                $this->data['total_hours'] = $this->calculate_total_hours($this->data['report_data']);
                $this->data['actual_start_date'] = $start_date;
                $this->data['actual_end_date'] = $end_date;

                // Calculate column totals for the footer
                $this->data['column_totals'] = [
                    'total_date_range' => $this->calculate_column_total($this->data['report_data'], 'total_date_range'),
                    'last_7_days' => $this->calculate_column_total($this->data['report_data'], 'last_7_days'),
                    'last_15_days' => $this->calculate_column_total($this->data['report_data'], 'last_15_days'),
                    'last_30_days' => $this->calculate_column_total($this->data['report_data'], 'last_30_days'),
                    'last_60_days' => $this->calculate_column_total($this->data['report_data'], 'last_60_days'),
                    'last_90_days' => $this->calculate_column_total($this->data['report_data'], 'last_90_days'),
                    'last_180_days' => $this->calculate_column_total($this->data['report_data'], 'last_180_days'),
                    'total_worked' => $this->calculate_column_total($this->data['report_data'], 'total_worked')
                ];

                // Calculate employee cost totals
                $this->data['emp_cost_totals'] = [
                    'total_date_range_emp_cost' => $this->calculate_cost_total($this->data['report_data'], 'total_date_range_emp_cost'),
                    'last_7_days_emp_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_7_days_emp_cost'),
                    'last_15_days_emp_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_15_days_emp_cost'),
                    'last_30_days_emp_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_30_days_emp_cost'),
                    'last_60_days_emp_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_60_days_emp_cost'),
                    'last_90_days_emp_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_90_days_emp_cost'),
                    'last_180_days_emp_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_180_days_emp_cost'),
                    'total_worked_emp_cost' => $this->calculate_cost_total($this->data['report_data'], 'total_worked_emp_cost')
                ];

                // Calculate project cost totals
                $this->data['proj_cost_totals'] = [
                    'total_date_range_proj_cost' => $this->calculate_cost_total($this->data['report_data'], 'total_date_range_proj_cost'),
                    'last_7_days_proj_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_7_days_proj_cost'),
                    'last_15_days_proj_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_15_days_proj_cost'),
                    'last_30_days_proj_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_30_days_proj_cost'),
                    'last_60_days_proj_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_60_days_proj_cost'),
                    'last_90_days_proj_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_90_days_proj_cost'),
                    'last_180_days_proj_cost' => $this->calculate_cost_total($this->data['report_data'], 'last_180_days_proj_cost'),
                    'total_worked_proj_cost' => $this->calculate_cost_total($this->data['report_data'], 'total_worked_proj_cost')
                ];
            } else {
                $this->data['error_message'] = $date_error;
            }
        }

        // Get projects for dropdown
        $this->data['projects'] = array_column(
            $this->projects_m->get(null, ['id', 'title'], ['key' => 'title', 'direction' => 'ASC'])->result_array(),
            'title',
            'id'
        );

        // Get project hourly rate for calculations
        if (!empty($project_id)) {
            $project_info = $this->projects_m->get($project_id)->row_array();
            $this->data['project_hourly_rate'] = floatval($project_info['hourly_rate'] ?? 0);
        } else {
            $this->data['project_hourly_rate'] = 0;
        }

        $this->data['page_title'] = 'Project Employee Report';
        $this->data['page_name'] = 'project_report/project_employee';
        $this->load->view('app/index', $this->data);
    }

    private function get_project_employee_report($project_id, $start_date, $end_date) {
        // Get project hourly rate
        $project_info = $this->projects_m->get($project_id)->row_array();
        $project_hourly_rate = floatval($project_info['hourly_rate'] ?? 0);
        // Get all employees who have ever worked on this project (regardless of date range)
        // Include both active (employee_status = 1) and inactive (employee_status = 0) employees if they have work records
        $this->db->distinct();
        $this->db->select('ta.user_id, u.name as employee_name, u.employee_status, u.hourly_rate');
        $this->db->from('task_assign ta');
        $this->db->join('tasks t', 't.id = ta.task_id');
        $this->db->join('users u', 'u.id = ta.user_id');
        $this->db->where('t.project_id', $project_id);
        $this->db->where('ta.time_taken IS NOT NULL');
        $this->db->where('ta.time_taken !=', '00:00:00');
        $this->db->order_by('u.employee_status', 'DESC'); // Active employees first
        $this->db->order_by('u.name', 'ASC');

        $employees = $this->db->get()->result_array();

        $report_data = [];

        foreach ($employees as $employee) {
            $user_id = $employee['user_id'];
            $employee_name = $employee['employee_name'];
            $employee_status = $employee['employee_status'];
            $hourly_rate = floatval($employee['hourly_rate'] ?? 0);

            // Calculate hours for different time periods relative to the end_date
            $total_date_range = $this->get_employee_project_hours($user_id, $project_id, $start_date, $end_date);

            // Calculate time periods from end_date backwards
            $end_date_obj = new DateTime($end_date);

            $last_7_start = clone $end_date_obj;
            $last_7_start->modify('-6 days'); // 7 days including end_date
            $last_7_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_7_start->format('Y-m-d'), $end_date);

            $last_15_start = clone $end_date_obj;
            $last_15_start->modify('-14 days'); // 15 days including end_date
            $last_15_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_15_start->format('Y-m-d'), $end_date);

            $last_30_start = clone $end_date_obj;
            $last_30_start->modify('-29 days'); // 30 days including end_date
            $last_30_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_30_start->format('Y-m-d'), $end_date);

            $last_60_start = clone $end_date_obj;
            $last_60_start->modify('-59 days'); // 60 days including end_date
            $last_60_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_60_start->format('Y-m-d'), $end_date);

            $last_90_start = clone $end_date_obj;
            $last_90_start->modify('-89 days'); // 90 days including end_date
            $last_90_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_90_start->format('Y-m-d'), $end_date);

            $last_180_start = clone $end_date_obj;
            $last_180_start->modify('-179 days'); // 180 days including end_date
            $last_180_days = $this->get_employee_project_hours($user_id, $project_id,
                $last_180_start->format('Y-m-d'), $end_date);

            // Calculate total worked (all time for this employee on this project)
            $total_worked = $this->get_employee_project_hours($user_id, $project_id, '1900-01-01', date('Y-m-d'));

            // Calculate costs (employee hourly_rate * hours worked)
            $total_date_range_emp_cost = $this->calculate_cost($total_date_range, $hourly_rate);
            $last_7_days_emp_cost = $this->calculate_cost($last_7_days, $hourly_rate);
            $last_15_days_emp_cost = $this->calculate_cost($last_15_days, $hourly_rate);
            $last_30_days_emp_cost = $this->calculate_cost($last_30_days, $hourly_rate);
            $last_60_days_emp_cost = $this->calculate_cost($last_60_days, $hourly_rate);
            $last_90_days_emp_cost = $this->calculate_cost($last_90_days, $hourly_rate);
            $last_180_days_emp_cost = $this->calculate_cost($last_180_days, $hourly_rate);
            $total_worked_emp_cost = $this->calculate_cost($total_worked, $hourly_rate);

            // Calculate project costs (project hourly_rate * hours worked)
            $total_date_range_proj_cost = $this->calculate_cost($total_date_range, $project_hourly_rate);
            $last_7_days_proj_cost = $this->calculate_cost($last_7_days, $project_hourly_rate);
            $last_15_days_proj_cost = $this->calculate_cost($last_15_days, $project_hourly_rate);
            $last_30_days_proj_cost = $this->calculate_cost($last_30_days, $project_hourly_rate);
            $last_60_days_proj_cost = $this->calculate_cost($last_60_days, $project_hourly_rate);
            $last_90_days_proj_cost = $this->calculate_cost($last_90_days, $project_hourly_rate);
            $last_180_days_proj_cost = $this->calculate_cost($last_180_days, $project_hourly_rate);
            $total_worked_proj_cost = $this->calculate_cost($total_worked, $project_hourly_rate);

            $report_data[] = [
                'user_id' => $user_id,
                'employee_name' => $employee_name,
                'employee_status' => $employee_status,
                'hourly_rate' => $hourly_rate,
                'total_date_range' => $total_date_range,
                'last_7_days' => $last_7_days,
                'last_15_days' => $last_15_days,
                'last_30_days' => $last_30_days,
                'last_60_days' => $last_60_days,
                'last_90_days' => $last_90_days,
                'last_180_days' => $last_180_days,
                'total_worked' => $total_worked,
                // Employee costs
                'total_date_range_emp_cost' => $total_date_range_emp_cost,
                'last_7_days_emp_cost' => $last_7_days_emp_cost,
                'last_15_days_emp_cost' => $last_15_days_emp_cost,
                'last_30_days_emp_cost' => $last_30_days_emp_cost,
                'last_60_days_emp_cost' => $last_60_days_emp_cost,
                'last_90_days_emp_cost' => $last_90_days_emp_cost,
                'last_180_days_emp_cost' => $last_180_days_emp_cost,
                'total_worked_emp_cost' => $total_worked_emp_cost,
                // Project costs
                'total_date_range_proj_cost' => $total_date_range_proj_cost,
                'last_7_days_proj_cost' => $last_7_days_proj_cost,
                'last_15_days_proj_cost' => $last_15_days_proj_cost,
                'last_30_days_proj_cost' => $last_30_days_proj_cost,
                'last_60_days_proj_cost' => $last_60_days_proj_cost,
                'last_90_days_proj_cost' => $last_90_days_proj_cost,
                'last_180_days_proj_cost' => $last_180_days_proj_cost,
                'total_worked_proj_cost' => $total_worked_proj_cost
            ];
        }

        return $report_data;
    }

    private function get_employee_project_hours($user_id, $project_id, $start_date, $end_date) {
        $this->db->select('ta.time_taken');
        $this->db->from('task_assign ta');
        $this->db->join('tasks t', 't.id = ta.task_id');
        $this->db->where('ta.user_id', $user_id);
        $this->db->where('t.project_id', $project_id);
        $this->db->where('ta.job_date >=', $start_date);
        $this->db->where('ta.job_date <=', $end_date);
        $this->db->where('ta.time_taken IS NOT NULL');
        $this->db->where('ta.time_taken !=', '00:00:00');
        $this->db->where('ta.time_taken <', '06:00:00'); // Filter out invalid entries

        $time_entries = $this->db->get()->result_array();
        $time_taken_array = array_column($time_entries, 'time_taken');

        // Filter out any null or empty values
        $time_taken_array = array_filter($time_taken_array, function($time) {
            return !empty($time) && $time !== '00:00:00';
        });

        if (empty($time_taken_array)) {
            return '0 hrs 0 min';
        }

        $total_seconds = add_duration($time_taken_array, 'seconds');
        return $this->format_readable_time($total_seconds);
    }

    private function calculate_total_hours($report_data) {
        if (empty($report_data)) {
            return '0 hrs 0 min';
        }

        $total_seconds = 0;

        foreach ($report_data as $employee_data) {
            if (isset($employee_data['total_date_range'])) {
                $total_seconds += $this->readable_time_to_seconds($employee_data['total_date_range']);
            }
        }

        return $this->format_readable_time($total_seconds);
    }

    private function calculate_column_total($report_data, $column) {
        if (empty($report_data)) {
            return '0 hrs 0 min';
        }

        $total_seconds = 0;

        foreach ($report_data as $employee_data) {
            if (isset($employee_data[$column])) {
                $total_seconds += $this->readable_time_to_seconds($employee_data[$column]);
            }
        }

        return $this->format_readable_time($total_seconds);
    }

    private function format_readable_time($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        return $hours . ' hrs ' . $minutes . ' min';
    }

    private function readable_time_to_seconds($readable_time) {
        // Convert "45 hrs 30 min" back to seconds for calculations
        if (preg_match('/(\d+)\s*hrs?\s*(\d+)\s*min/', $readable_time, $matches)) {
            $hours = (int)$matches[1];
            $minutes = (int)$matches[2];
            return ($hours * 3600) + ($minutes * 60);
        }
        return 0;
    }

    private function calculate_cost($time_string, $hourly_rate) {
        if ($hourly_rate <= 0) {
            return '0.00';
        }

        $seconds = $this->readable_time_to_seconds($time_string);
        $hours = $seconds / 3600; // Convert seconds to hours
        $cost = $hours * $hourly_rate;

        return number_format($cost, 2);
    }

    private function calculate_cost_total($report_data, $cost_column) {
        if (empty($report_data)) {
            return '0.00';
        }

        $total_cost = 0;

        foreach ($report_data as $employee_data) {
            if (isset($employee_data[$cost_column])) {
                $total_cost += floatval($employee_data[$cost_column]);
            }
        }

        return number_format($total_cost, 2);
    }


}
