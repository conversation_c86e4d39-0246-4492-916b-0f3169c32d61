<?php
$project_id = $this->input->get('project_id') ?? '';
$start_date = $this->input->get('start_date') ?? '';
$end_date = $this->input->get('end_date') ?? '';
?>

<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
    </div>
    
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title text-center">
                <?= strtoupper($page_title ?? '')?>
            </h3>
        </div>
        
        <div class="card-body p-1">
            <form method="get" action="">
                <div class="shadow_pro row p-1" style="max-width: 1200px;">
                    <div class="col-12 col-lg-4 p-2">
                        <select name="project_id" id="project_id" class="form-control select2" required>
                            <option value="">Choose Project</option>
                            <?php
                            if (isset($projects)){
                                foreach ($projects as $id => $title){
                                    $selected = $id == $project_id ? 'selected' : '';
                                    echo "<option value='{$id}' {$selected}>{$title}</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-6 col-lg-3 p-2">
                        <input type="date" name="start_date" id="start_date" value="<?= $start_date ?>"
                               class="form-control" placeholder="Start Date (Optional)">
                        <small class="text-muted">Leave empty for last 30 days</small>
                    </div>
                    <div class="col-6 col-lg-3 p-2">
                        <input type="date" name="end_date" id="end_date" value="<?= $end_date ?>"
                               class="form-control" placeholder="End Date (Optional)">
                        <small class="text-muted">Leave empty for today</small>
                    </div>
                    <div class="col-6 col-lg-2 p-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-chart-bar"></i> Generate Report
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php if (!empty($error_message)): ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-4">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Error</h5>
                <p><?= htmlspecialchars($error_message) ?></p>
            </div>
        </div>
    </div>
</div>
<?php elseif (!empty($report_data)): ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="mb-3 p-3" style="background-color: #f8f9fa; border-radius: 5px;">
            <div class="row">
                <div class="col-md-8">
                    <h4 class="text-primary mb-2">
                        <i class="fas fa-users"></i> Project Employee Work Report
                    </h4>
                    <?php if (!empty($projects[$project_id])): ?>
                        <h6 class="text-muted mb-0">
                            <i class="fas fa-project-diagram"></i> <?= htmlspecialchars($projects[$project_id]) ?>
                        </h6>
                    <?php endif; ?>
                </div>
                <div class="col-md-4 text-right">
                    <small class="text-muted d-block">Report Period</small>
                    <strong>
                        <?= date('d-m-Y', strtotime($actual_start_date ?? $start_date)) ?> to
                        <?= date('d-m-Y', strtotime($actual_end_date ?? $end_date)) ?>
                    </strong>
                    <?php if (empty($start_date) || empty($end_date)): ?>
                        <br><small class="badge badge-info">Default: This month</small>
                    <?php endif; ?>
                    <?php if (!empty($report_data)): ?>
                        <br><small class="text-muted"><?= count($report_data) ?> Employees</small>
                        <br><small class="text-success font-weight-bold">Total Cost: ₹<?= $emp_cost_totals['total_date_range_emp_cost'] ?? '0.00' ?></small>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Report Table -->
        <div class="table-responsive" style="border: 1px solid #dee2e6; border-radius: 5px; background: white;">
            <table class="table table-bordered table-striped">
                <thead style="background-color: #f1f2f8;">
                    <tr>
                        <th class="employee-name-col">Employee Name</th>
                        <th class="text-center">Total Date Range<br><small class="text-muted">Hours | Emp Cost | Proj Cost</small></th>
                        <th class="text-center">Last 7 Days<br><small class="text-muted">Hours | Emp Cost | Proj Cost</small></th>
                        <th class="text-center">Last 15 Days<br><small class="text-muted">Hours | Emp Cost | Proj Cost</small></th>
                        <th class="text-center">Last 30 Days<br><small class="text-muted">Hours | Emp Cost | Proj Cost</small></th>
                        <th class="text-center">Last 60 Days<br><small class="text-muted">Hours | Emp Cost | Proj Cost</small></th>
                        <th class="text-center">Last 90 Days<br><small class="text-muted">Hours | Emp Cost | Proj Cost</small></th>
                        <th class="text-center">Last 180 Days<br><small class="text-muted">Hours | Emp Cost | Proj Cost</small></th>
                        <th class="text-center" style="background-color: #e8f5e8;">Total Worked<br><small class="text-muted">Hours | Emp Cost | Proj Cost</small></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($report_data as $index => $employee): ?>
                    <tr class="<?= $employee['employee_status'] == 1 ? 'active-employee' : 'inactive-employee' ?>">
                        <td class="employee-name-col">
                            <div class="employee-info">
                                <strong><?= htmlspecialchars($employee['employee_name']) ?></strong>
                                <div class="employee-status">
                                    <?php if ($employee['employee_status'] == 1): ?>
                                        <span class="badge badge-success badge-sm">Active</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger badge-sm">Inactive</span>
                                    <?php endif; ?>
                                    <?php if ($employee['hourly_rate'] > 0): ?>
                                        <small class="text-muted d-block">Cost: ₹<?= number_format($employee['hourly_rate'], 2) ?>/hr</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['total_date_range'] ?></div>
                            <div class="cost-display">₹<?= $employee['total_date_range_emp_cost'] ?> | ₹<?= $employee['total_date_range_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_7_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_7_days_emp_cost'] ?> | ₹<?= $employee['last_7_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_15_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_15_days_emp_cost'] ?> | ₹<?= $employee['last_15_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_30_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_30_days_emp_cost'] ?> | ₹<?= $employee['last_30_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_60_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_60_days_emp_cost'] ?> | ₹<?= $employee['last_60_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_90_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_90_days_emp_cost'] ?> | ₹<?= $employee['last_90_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_180_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_180_days_emp_cost'] ?> | ₹<?= $employee['last_180_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell total-worked-cell">
                            <div class="time-display font-weight-bold"><?= $employee['total_worked'] ?></div>
                            <div class="cost-display font-weight-bold">₹<?= $employee['total_worked_emp_cost'] ?> | ₹<?= $employee['total_worked_proj_cost'] ?></div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot style="background-color: #f1f2f8; font-weight: bold;">
                    <tr>
                        <th class="employee-name-col">
                            <strong>TOTALS</strong>
                        </th>
                        <th class="text-center">
                            <div class="total-time"><?= $column_totals['total_date_range'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost text-primary">₹<?= $emp_cost_totals['total_date_range_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['total_date_range_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center">
                            <div class="total-time"><?= $column_totals['last_7_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_7_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_7_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center">
                            <div class="total-time"><?= $column_totals['last_15_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_15_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_15_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center">
                            <div class="total-time"><?= $column_totals['last_30_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_30_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_30_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center">
                            <div class="total-time"><?= $column_totals['last_60_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_60_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_60_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center">
                            <div class="total-time"><?= $column_totals['last_90_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_90_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_90_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center">
                            <div class="total-time"><?= $column_totals['last_180_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_180_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_180_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center">
                            <div class="total-time text-success"><?= $column_totals['total_worked'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost text-success">₹<?= $emp_cost_totals['total_worked_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['total_worked_proj_cost'] ?? '0.00' ?></div>
                        </th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <?php if (empty($report_data)): ?>
        <div class="text-center p-4">
            <div class="alert alert-info">
                <h5>No Data Found</h5>
                <p>No employees worked on this project during the selected date range.</p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php else: ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-4">
            <div class="alert alert-warning">
                <h5><i class="fas fa-info-circle"></i> Generate Report</h5>
                <p>Please select a project to generate the employee work report.</p>
                <p><small class="text-muted">Date range is optional - if not specified, the report will show data for the last 30 days.</small></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
    /* Simple Professional Styling - Matching Existing Template */
    .employee-name-col {
        position: sticky;
        left: 0;
        z-index: 10;
        background: #f1f2f8 !important;
        min-width: 220px;
        border-right: 2px solid #dee2e6 !important;
    }

    .table th, .table td {
        padding: 12px 8px !important;
        vertical-align: middle;
        white-space: nowrap;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .badge-sm {
        font-size: 0.75rem;
        padding: 3px 8px;
        margin-top: 2px;
    }

    /* Employee Status Color Coding */
    .active-employee {
        background-color: rgba(40, 167, 69, 0.05) !important; /* Light green background */
        border-left: 3px solid #28a745;
    }

    .inactive-employee {
        background-color: rgba(220, 53, 69, 0.05) !important; /* Light red background */
        border-left: 3px solid #dc3545;
        opacity: 0.8;
    }

    /* Sticky employee name column for scrolling */
    tbody .employee-name-col {
        background: white !important;
        font-weight: bold;
        padding: 15px 12px !important;
    }

    .active-employee .employee-name-col {
        background: rgba(40, 167, 69, 0.05) !important;
    }

    .inactive-employee .employee-name-col {
        background: rgba(220, 53, 69, 0.05) !important;
    }

    /* Employee Info Layout */
    .employee-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .employee-status {
        margin-top: 4px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    /* Time and Cost Display */
    .time-cost-cell {
        padding: 10px 8px !important;
        min-width: 120px;
    }

    .time-display {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 2px;
        font-size: 0.9rem;
    }

    .cost-display {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 500;
    }

    .total-worked-cell .time-display {
        color: #28a745;
        font-weight: bold;
    }

    .total-worked-cell .cost-display {
        color: #28a745;
        font-weight: bold;
    }

    /* Footer Totals */
    .total-time {
        font-weight: bold;
        margin-bottom: 2px;
        font-size: 0.9rem;
    }

    .total-cost {
        font-size: 0.85rem;
        font-weight: 600;
    }

    /* Shadow for cards */
    .shadow-pro {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.375rem;
    }

    /* Hover effects */
    .table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }

    .active-employee:hover {
        background-color: rgba(40, 167, 69, 0.1) !important;
    }

    .inactive-employee:hover {
        background-color: rgba(220, 53, 69, 0.1) !important;
    }
</style>

<script>
$(document).ready(function() {
    $('.select2').select2({
        placeholder: "Choose Project",
        allowClear: true
    });
});
</script>
